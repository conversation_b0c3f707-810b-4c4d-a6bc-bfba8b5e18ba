const { createServer } = require('http')
const { Sender } = require('../dist/app/utils/sender/sender')

// Test configuration
const TEST_PORT = 3001
const TEST_URL = `http://localhost:${TEST_PORT}/test`

// Test results tracking
const results = []

function log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`)
}

function assert(condition, message) {
    if (condition) {
        log(`✅ PASS: ${message}`)
        results.push({ status: 'PASS', message })
    } else {
        log(`❌ FAIL: ${message}`)
        results.push({ status: 'FAIL', message })
    }
}

function createTestServer() {
    return createServer((req, res) => {
        const url = new URL(req.url, `http://${req.headers.host}`)
        const delay = parseInt(url.searchParams.get('delay') || '0')
        
        log(`Server received request with delay: ${delay}ms`)
        
        if (delay > 0) {
            setTimeout(() => {
                res.writeHead(200, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify({ message: 'success', delay }))
            }, delay)
        } else {
            res.writeHead(200, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ message: 'success' }))
        }
    })
}

async function testTimeout() {
    log('=== Testing Timeout Functionality ===')
    
    const sender = new Sender(TEST_URL, { timeout: 1000 }) // 1 second timeout
    
    try {
        // Test 1: Request should succeed within timeout
        log('Test 1: Request within timeout limit')
        const response1 = await sender.send('test', { timeout: 2000 })
        assert(response1.status === 200, 'Request within timeout should succeed')
        assert(response1.body.includes('success'), 'Response should contain success message')
        
        // Test 2: Request should timeout
        log('Test 2: Request exceeding timeout limit')
        try {
            await sender.send('test', { 
                headers: { 'X-Test': 'timeout' },
                metadata: { testCase: 'timeout' }
            })
            // Add delay parameter to make server delay response
            const slowUrl = `${TEST_URL}?delay=3000`
            const slowSender = new Sender(slowUrl, { timeout: 1000 })
            await slowSender.send('test')
            assert(false, 'Request should have timed out')
        } catch (error) {
            assert(error.name === 'SenderRequestError' || error.message.includes('timeout'), 'Should throw timeout error')
            log(`Timeout error caught: ${error.message}`)
        }
        
        // Test 3: Custom timeout in send options
        log('Test 3: Custom timeout in send options')
        try {
            const slowUrl = `${TEST_URL}?delay=2000`
            const customSender = new Sender(slowUrl, { timeout: 5000 })
            await customSender.send('test', { timeout: 500 }) // Override with shorter timeout
            assert(false, 'Request should have timed out with custom timeout')
        } catch (error) {
            assert(error.message.includes('timeout') || error.name === 'SenderRequestError', 'Should timeout with custom timeout')
        }
        
    } catch (error) {
        log(`Unexpected error in timeout tests: ${error.message}`)
        assert(false, `Timeout test failed unexpectedly: ${error.message}`)
    }
}

async function testAbort() {
    log('=== Testing Abort Functionality ===')
    
    const sender = new Sender(TEST_URL, { timeout: 10000 }) // Long timeout
    
    try {
        // Test 1: Manual abort with AbortController
        log('Test 1: Manual abort with AbortController')
        const controller = new AbortController()
        
        // Abort after 500ms
        setTimeout(() => {
            log('Aborting request manually')
            controller.abort()
        }, 500)
        
        try {
            const slowUrl = `${TEST_URL}?delay=2000`
            const abortSender = new Sender(slowUrl, { timeout: 10000 })
            await abortSender.send('test', { 
                signal: controller.signal,
                metadata: { testCase: 'manual-abort' }
            })
            assert(false, 'Request should have been aborted')
        } catch (error) {
            assert(
                error.message.includes('abort') || error.name === 'AbortError' || error.name === 'SenderRequestError',
                'Should throw abort error'
            )
            log(`Abort error caught: ${error.message}`)
        }
        
        // Test 2: Abort with reason
        log('Test 2: Abort with custom reason')
        const controller2 = new AbortController()
        
        setTimeout(() => {
            controller2.abort('Custom abort reason')
        }, 300)
        
        try {
            const slowUrl = `${TEST_URL}?delay=1000`
            const abortSender2 = new Sender(slowUrl)
            await abortSender2.send('test', { signal: controller2.signal })
            assert(false, 'Request should have been aborted with reason')
        } catch (error) {
            assert(
                error.message.includes('abort') || error.name === 'AbortError' || error.name === 'SenderRequestError',
                'Should throw abort error with reason'
            )
        }
        
        // Test 3: Pre-aborted signal
        log('Test 3: Pre-aborted signal')
        const preAbortedController = new AbortController()
        preAbortedController.abort('Pre-aborted')
        
        try {
            await sender.send('test', { signal: preAbortedController.signal })
            assert(false, 'Request should fail with pre-aborted signal')
        } catch (error) {
            assert(
                error.message.includes('abort') || error.name === 'AbortError' || error.name === 'SenderRequestError',
                'Should immediately fail with pre-aborted signal'
            )
        }
        
    } catch (error) {
        log(`Unexpected error in abort tests: ${error.message}`)
        assert(false, `Abort test failed unexpectedly: ${error.message}`)
    }
}

async function testCombinedTimeoutAndAbort() {
    log('=== Testing Combined Timeout and Abort ===')
    
    const sender = new Sender(TEST_URL, { timeout: 2000 })
    
    try {
        // Test: Abort should win over timeout
        log('Test: Abort before timeout')
        const controller = new AbortController()
        
        setTimeout(() => {
            controller.abort('Manual abort wins')
        }, 500)
        
        try {
            const slowUrl = `${TEST_URL}?delay=1500`
            const combinedSender = new Sender(slowUrl, { timeout: 3000 })
            await combinedSender.send('test', { signal: controller.signal })
            assert(false, 'Request should have been aborted before timeout')
        } catch (error) {
            assert(
                error.message.includes('abort') || error.name === 'AbortError' || error.name === 'SenderRequestError',
                'Abort should take precedence over timeout'
            )
        }
        
    } catch (error) {
        log(`Unexpected error in combined tests: ${error.message}`)
        assert(false, `Combined test failed unexpectedly: ${error.message}`)
    }
}

async function runTests() {
    log('Starting Sender timeout and abort tests...')
    
    // Create and start test server
    const server = createTestServer()
    
    return new Promise((resolve) => {
        server.listen(TEST_PORT, async () => {
            log(`Test server started on port ${TEST_PORT}`)
            
            try {
                await testTimeout()
                await testAbort()
                await testCombinedTimeoutAndAbort()
                
                // Print results summary
                log('\n=== Test Results Summary ===')
                const passed = results.filter(r => r.status === 'PASS').length
                const failed = results.filter(r => r.status === 'FAIL').length
                
                log(`Total tests: ${results.length}`)
                log(`Passed: ${passed}`)
                log(`Failed: ${failed}`)
                
                if (failed > 0) {
                    log('\nFailed tests:')
                    results.filter(r => r.status === 'FAIL').forEach(r => {
                        log(`  - ${r.message}`)
                    })
                }
                
                log(`\nOverall result: ${failed === 0 ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)
                
            } catch (error) {
                log(`Test execution error: ${error.message}`)
                log(error.stack)
            } finally {
                server.close(() => {
                    log('Test server stopped')
                    resolve()
                })
            }
        })
    })
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().then(() => {
        process.exit(results.some(r => r.status === 'FAIL') ? 1 : 0)
    }).catch((error) => {
        console.error('Test runner error:', error)
        process.exit(1)
    })
}

module.exports = { runTests }
